import { useState } from 'react'
import { Row, Col, Card, Button, Tree, Input, Modal, Form, Select, Space } from 'antd'
import type { DataNode } from 'antd/es/tree'
import { PageWrapper } from '@/components/Page'

import { PlusOutlined, EditOutlined, DeleteOutlined } from '@ant-design/icons'

const { TreeNode } = Tree
const { Option } = Select

interface CategoryItem {
  label: string
  value: string
  level?: number
  children?: CategoryItem[]
}

// 转换为 Tree 所需的数据结构
const generateTreeData = (data: CategoryItem[]): DataNode[] => {
  return data.map(item => ({
    title: item.label,
    key: item.value,
    children: item.children ? generateTreeData(item.children) : undefined
  }))
}

// 初始数据
const initialCategoryData: CategoryItem[] = [
  {
    label: '数据资源分类',
    value: 'data_resource',
    level: 1,
    children: [
      {
        label: '公共数据',
        value: 'public',
        level: 2,
        children: [
          { label: '城市管理', value: 'city', level: 3 },
          { label: '环境保护', value: 'environment', level: 3 },
          { label: '城乡建设', value: 'urban', level: 3 },
          { label: '国土资源', value: 'land', level: 3 },
          { label: '综合政务', value: 'gov', level: 3 }
        ]
      },
      {
        label: '行业数据',
        value: 'industry',
        level: 2,
        children: [
          { label: '金融行业', value: 'finance', level: 3 },
          { label: '制造行业', value: 'manufacture', level: 3 },
          { label: '医疗行业', value: 'medical', level: 3 }
        ]
      }
    ]
  }
]

const initialLevelData: CategoryItem[] = [
  {
    label: '公开访问',
    value: 'public',
    children: [{ label: '公开访问数据', value: 'public_data' }]
  },
  {
    label: '指定访问',
    value: 'restricted',
    children: [
      { label: '指定外部合作方可访问数据', value: 'restricted_data' },
      { label: '重要级数据', value: 'important_data' }
    ]
  }
]

const DirectoryManagement = () => {
  const [categoryData, setCategoryData] = useState<CategoryItem[]>(initialCategoryData)
  const [levelData, setLevelData] = useState<CategoryItem[]>(initialLevelData)
  const [expandedKeys, setExpandedKeys] = useState<React.Key[]>(['data_resource', 'public', 'industry'])
  const [levelExpandedKeys, setLevelExpandedKeys] = useState<React.Key[]>(['public', 'restricted'])
  const [selectedKeys, setSelectedKeys] = useState<React.Key[]>([])
  const [levelSelectedKeys, setLevelSelectedKeys] = useState<React.Key[]>([])
  const [checkedKeys, setCheckedKeys] = useState<React.Key[]>([])
  const [levelCheckedKeys, setLevelCheckedKeys] = useState<React.Key[]>([])
  const [isModalVisible, setIsModalVisible] = useState(false)
  const [isLevelModalVisible, setIsLevelModalVisible] = useState(false)
  const [currentNodeType, setCurrentNodeType] = useState<'root' | 'child'>('root')
  const [currentNode, setCurrentNode] = useState<any>(null)
  const [form] = Form.useForm()
  const [levelForm] = Form.useForm()

  // 处理树节点展开/收起
  const onExpand = (keys: React.Key[]) => {
    setExpandedKeys(keys)
  }

  const onLevelExpand = (keys: React.Key[]) => {
    setLevelExpandedKeys(keys)
  }

  // 处理树节点选择
  const onSelect = (keys: React.Key[], info: any) => {
    setSelectedKeys(keys)
    if (keys.length > 0) {
      setCurrentNode(info.node)
      setCurrentNodeType(info.node.children ? 'root' : 'child')
    }
  }

  const onLevelSelect = (keys: React.Key[], info: any) => {
    setLevelSelectedKeys(keys)
    if (keys.length > 0) {
      setCurrentNode(info.node)
      setCurrentNodeType(info.node.children ? 'root' : 'child')
    }
  }

  // 处理树节点勾选
  const onCheck = (keys: React.Key[] | { checked: React.Key[]; halfChecked: React.Key[] }) => {
    if (Array.isArray(keys)) {
      setCheckedKeys(keys)
    } else {
      setCheckedKeys(keys.checked)
    }
  }

  const onLevelCheck = (keys: React.Key[] | { checked: React.Key[]; halfChecked: React.Key[] }) => {
    if (Array.isArray(keys)) {
      setLevelCheckedKeys(keys)
    } else {
      setLevelCheckedKeys(keys.checked)
    }
  }

  const handleLevelAdd = (nodeType: 'root' | 'child') => {
    setCurrentNodeType(nodeType)
    levelForm.resetFields()
    setIsLevelModalVisible(true)
  }

  // 处理添加表单提交
  const handleAddSubmit = () => {
    form.validateFields().then(values => {
      if (currentNodeType === 'root') {
        // 添加根节点（一级分类）
        setCategoryData([
          ...categoryData,
          {
            label: values.name,
            value: values.key,
            level: 1,
            children: []
          }
        ])
      } else if (currentNode && currentNode.key) {
        // 添加子节点
        const addChildToData = (data: CategoryItem[], parentKey: string): CategoryItem[] => {
          return data.map(item => {
            if (item.value === parentKey) {
              const parentLevel = item.level || getNodeLevel(item.value, categoryData)
              if (!item.children) {
                item.children = []
              }
              item.children.push({
                label: values.name,
                value: values.key,
                level: parentLevel + 1
              })
              return { ...item }
            }
            if (item.children) {
              return {
                ...item,
                children: addChildToData(item.children, parentKey)
              }
            }
            return item
          })
        }
        setCategoryData(addChildToData(categoryData, currentNode.key))
      }
      setIsModalVisible(false)
    })
  }

  const handleLevelAddSubmit = () => {
    levelForm.validateFields().then(values => {
      if (currentNodeType === 'root') {
        // 添加根节点
        setLevelData([
          ...levelData,
          {
            label: values.name,
            value: values.key,
            children: []
          }
        ])
      } else if (levelSelectedKeys.length > 0) {
        // 添加子节点
        const newData = [...levelData]
        const parentKey = levelSelectedKeys[0].toString()
        const parentNode = newData.find(item => item.value === parentKey)

        if (parentNode) {
          if (!parentNode.children) {
            parentNode.children = []
          }
          parentNode.children.push({
            label: values.name,
            value: values.key
          })
          setLevelData(newData)
        }
      }
      setIsLevelModalVisible(false)
    })
  }

  // 获取节点层级
  const getNodeLevel = (nodeKey: string, data: CategoryItem[], currentLevel = 1): number => {
    for (const item of data) {
      if (item.value === nodeKey) {
        return currentLevel
      }
      if (item.children) {
        const childLevel = getNodeLevel(nodeKey, item.children, currentLevel + 1)
        if (childLevel > 0) return childLevel
      }
    }
    return 0
  }

  // 删除节点
  const handleDelete = (nodeKey: string) => {
    const deleteFromData = (data: CategoryItem[]): CategoryItem[] => {
      return data.filter(item => {
        if (item.value === nodeKey) {
          return false
        }
        if (item.children) {
          item.children = deleteFromData(item.children)
        }
        return true
      })
    }
    setCategoryData(deleteFromData(categoryData))
  }

  // 添加子节点
  const handleAddChild = (parentKey: string) => {
    setCurrentNode({ key: parentKey })
    setCurrentNodeType('child')
    form.resetFields()
    setIsModalVisible(true)
  }

  // 渲染树节点标题（包含操作按钮）
  const renderNodeTitle = (item: CategoryItem) => {
    const level = item.level || getNodeLevel(item.value, categoryData)

    return (
      <div
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          width: '100%',
          paddingRight: '8px'
        }}
      >
        <span>{item.label}</span>
        <div style={{ display: 'flex', gap: '4px', marginLeft: 'auto' }}>
          {/* 只有二级节点可以添加三级子节点 */}
          {level === 2 && (
            <Button
              type='text'
              size='small'
              icon={<PlusOutlined />}
              style={{
                minWidth: '24px',
                height: '24px',
                padding: '0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
              onClick={e => {
                e.stopPropagation()
                handleAddChild(item.value)
              }}
            />
          )}
          {/* 二级和三级节点都可以删除 */}
          {level >= 2 && (
            <Button
              type='text'
              size='small'
              icon={<DeleteOutlined />}
              style={{
                minWidth: '24px',
                height: '24px',
                padding: '0',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
              onClick={e => {
                e.stopPropagation()
                handleDelete(item.value)
              }}
            />
          )}
        </div>
      </div>
    )
  }

  // 渲染树节点
  const renderTreeNodes = (data: CategoryItem[]) => {
    return data.map(item => {
      if (item.children) {
        return (
          <TreeNode title={renderNodeTitle(item)} key={item.value}>
            {renderTreeNodes(item.children)}
          </TreeNode>
        )
      }
      return <TreeNode title={renderNodeTitle(item)} key={item.value} />
    })
  }

  return (
    // <PageWrapper plugin={TREE_COMPO}>
    <>
      <Row gutter={24}>
        <Col span={12}>
          <Card
            title={
              <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', width: '100%' }}>
                <span>数据目录分类</span>
                <Button
                  type='primary'
                  icon={<PlusOutlined />}
                  onClick={() => {
                    // 只能添加二级分类到数据资源分类下
                    setCurrentNode({ key: 'data_resource' })
                    setCurrentNodeType('child')
                    form.resetFields()
                    setIsModalVisible(true)
                  }}
                >
                  添加二级分类
                </Button>
              </div>
            }
          >
            <Tree
              checkable
              onExpand={onExpand}
              expandedKeys={expandedKeys}
              onSelect={onSelect}
              selectedKeys={selectedKeys}
              onCheck={onCheck}
              checkedKeys={checkedKeys}
            >
              {renderTreeNodes(categoryData)}
            </Tree>
            <Button type='primary' style={{ marginTop: 24 }}>
              提交
            </Button>
          </Card>
        </Col>
        <Col span={12}>
          <Card
            title='数据目录分级'
            bordered={false}
            extra={
              <div>
                <Button
                  type='primary'
                  icon={<PlusOutlined />}
                  style={{ marginRight: 8 }}
                  onClick={() => handleLevelAdd('root')}
                >
                  添加分级
                </Button>
                {levelSelectedKeys.length > 0 && (
                  <Button icon={<PlusOutlined />} onClick={() => handleLevelAdd('child')}>
                    添加子级
                  </Button>
                )}
              </div>
            }
          >
            <div style={{ fontWeight: 500, marginBottom: 8 }}>访问控制分级</div>
            <Tree
              checkable
              onExpand={onLevelExpand}
              expandedKeys={levelExpandedKeys}
              onSelect={onLevelSelect}
              selectedKeys={levelSelectedKeys}
              onCheck={onLevelCheck}
              checkedKeys={levelCheckedKeys}
            >
              {renderTreeNodes(levelData)}
            </Tree>
            <Button type='primary' style={{ marginTop: 24 }}>
              确认
            </Button>
          </Card>
        </Col>
      </Row>

      {/* 添加分类的Modal */}
      <Modal
        title={
          currentNodeType === 'root'
            ? '添加一级分类'
            : currentNode?.key === 'data_resource'
              ? '添加二级分类'
              : '添加三级分类'
        }
        open={isModalVisible}
        onOk={handleAddSubmit}
        onCancel={() => setIsModalVisible(false)}
      >
        <Form form={form} layout='vertical'>
          <Form.Item name='name' label='分类名称' rules={[{ required: true, message: '请输入分类名称' }]}>
            <Input placeholder='请输入分类名称' />
          </Form.Item>
          <Form.Item name='key' label='分类标识' rules={[{ required: true, message: '请输入分类标识' }]}>
            <Input placeholder='请输入分类标识' />
          </Form.Item>
        </Form>
      </Modal>

      {/* 添加分级的Modal */}
      <Modal
        title={currentNodeType === 'root' ? '添加分级' : '添加子级'}
        open={isLevelModalVisible}
        onOk={handleLevelAddSubmit}
        onCancel={() => setIsLevelModalVisible(false)}
      >
        <Form form={levelForm} layout='vertical'>
          <Form.Item name='name' label='分级名称' rules={[{ required: true, message: '请输入分级名称' }]}>
            <Input placeholder='请输入分级名称' />
          </Form.Item>
          <Form.Item name='key' label='分级标识' rules={[{ required: true, message: '请输入分级标识' }]}>
            <Input placeholder='请输入分级标识' />
          </Form.Item>
        </Form>
      </Modal>
    </>
    // </PageWrapper>
  )
}

export default DirectoryManagement
