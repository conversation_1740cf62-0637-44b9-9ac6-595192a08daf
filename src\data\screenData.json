{"description": "大屏展示数据 - 用于演示效果，替代真实接口请求", "data": {"counts": "30000", "xData": ["01-01", "01-02", "01-03", "01-04", "01-05", "01-06", "01-07", "01-08", "01-09", "01-10"], "yData": [120, 132, 101, 134, 90, 230, 210, 182, 191, 234], "listedCount": 15000, "listedCompareRate": 50, "signCount": 6000, "signCompareRate": 20, "tradedCount": 40, "pendingCount": 20, "exchanges": [{"dataExName": "杭州数据交易所", "signedContractsCount": 30, "distinctBuyerUsersCount": 10, "listedDataCount": 1000}, {"dataExName": "郑州数据交易所", "signedContractsCount": 25, "distinctBuyerUsersCount": 8, "listedDataCount": 800}, {"dataExName": "深圳数据交易所", "signedContractsCount": 35, "distinctBuyerUsersCount": 12, "listedDataCount": 1200}, {"dataExName": "北京数据交易所", "signedContractsCount": 40, "distinctBuyerUsersCount": 15, "listedDataCount": 1500}, {"dataExName": "上海数据交易所", "signedContractsCount": 45, "distinctBuyerUsersCount": 18, "listedDataCount": 1800}, {"dataExName": "广州数据交易所", "signedContractsCount": 28, "distinctBuyerUsersCount": 9, "listedDataCount": 900}], "dataList": {"records": [{"dataId": "1", "dataName": "智慧城市交通数据", "dataRegisterNumber": "A0001111", "listedNumber": "L0001111", "contractNumber": "C0001111", "dataCategory": "公共数据", "dataSecondCategory": "城市管理", "dataAccessLevel": "公开"}, {"dataId": "2", "dataName": "金融风控数据集", "dataRegisterNumber": "A0001112", "listedNumber": "L0001112", "contractNumber": "C0001112", "dataCategory": "企业数据", "dataSecondCategory": "金融", "dataAccessLevel": "指定机构"}, {"dataId": "3", "dataName": "医疗健康数据", "dataRegisterNumber": "A0001113", "listedNumber": "L0001113", "contractNumber": "C0001113", "dataCategory": "公共数据", "dataSecondCategory": "医疗健康", "dataAccessLevel": "受限"}, {"dataId": "4", "dataName": "电商用户行为数据", "dataRegisterNumber": "A0001114", "listedNumber": "L0001114", "contractNumber": "C0001114", "dataCategory": "企业数据", "dataSecondCategory": "电子商务", "dataAccessLevel": "指定机构"}, {"dataId": "5", "dataName": "物流配送数据", "dataRegisterNumber": "A0001115", "listedNumber": "L0001115", "contractNumber": "C0001115", "dataCategory": "企业数据", "dataSecondCategory": "物流", "dataAccessLevel": "公开"}], "total": 9, "current": 1, "size": 10}, "identityData": {"socialUnifiedCreditCode": "91330100MA28A1234X", "enterpriseName": "杭州数据科技有限公司", "legalPerson": "张三", "registrationDate": "2023-01-15", "businessScope": "数据服务、技术开发"}, "tokenData": {"socialUnifiedCreditCode": "TOKEN_001", "enterpriseName": "杭州数据科技有限公司", "tokenId": "TOKEN_001", "tokenName": "智慧城市数据通证", "issueDate": "2023-06-01", "validityPeriod": "2024-06-01", "tokenType": "数据使用权证"}, "directoryData": {"dataRegisterNumber": "A0001111", "listedNumber": "L0001111", "dataFirstCategory": "公共数据", "dataSecondCategory": "城市管理", "dataAccessLevel": "公开", "transactionHash": "0x1234567890abcdef1234567890abcdef", "dataExName": "杭州数据交易所"}, "signData": {"records": [{"dataName": "智慧城市交通数据", "contractNumber": "C0001111", "contractStartAt": "2023-01-01", "contractEndAt": "2023-12-31", "status": 1, "contractId": "CONTRACT_001", "buyerEnterpriseName": "北京科技有限公司", "sellerEnterpriseName": "杭州数据科技有限公司"}, {"dataName": "金融风控数据集", "contractNumber": "C0001112", "contractStartAt": "2023-02-01", "contractEndAt": "2024-01-31", "status": 1, "contractId": "CONTRACT_002", "buyerEnterpriseName": "上海金融科技公司", "sellerEnterpriseName": "深圳数据服务公司"}, {"dataName": "医疗健康数据", "contractNumber": "C0001113", "contractStartAt": "2023-03-01", "contractEndAt": "2024-02-28", "status": 1, "contractId": "CONTRACT_003", "buyerEnterpriseName": "广州医疗集团", "sellerEnterpriseName": "成都健康数据公司"}, {"dataName": "电商用户行为数据", "contractNumber": "C0001114", "contractStartAt": "2023-04-01", "contractEndAt": "2024-03-31", "status": 1, "contractId": "CONTRACT_004", "buyerEnterpriseName": "杭州电商平台", "sellerEnterpriseName": "阿里巴巴数据公司"}, {"dataName": "物流配送数据", "contractNumber": "C0001115", "contractStartAt": "2023-05-01", "contractEndAt": "2024-04-30", "status": 1, "contractId": "CONTRACT_005", "buyerEnterpriseName": "顺丰科技", "sellerEnterpriseName": "京东物流数据"}], "total": 5}, "orderData": {"records": [{"dataName": "智慧城市交通数据计算订单", "contractNumber": "ORDER_001", "contractStartAt": "2023-01-01 09:00:00", "contractEndAt": "2023-01-01 18:00:00", "taskCount": 5, "contractId": "CONTRACT_001", "orderStatus": "已完成", "computeType": "隐私求交"}, {"dataName": "金融风控数据计算订单", "contractNumber": "ORDER_002", "contractStartAt": "2023-01-02 09:00:00", "contractEndAt": "2023-01-02 18:00:00", "taskCount": 3, "contractId": "CONTRACT_002", "orderStatus": "进行中", "computeType": "联邦学习"}, {"dataName": "医疗健康数据计算订单", "contractNumber": "ORDER_003", "contractStartAt": "2023-01-03 09:00:00", "contractEndAt": "2023-01-03 18:00:00", "taskCount": 7, "contractId": "CONTRACT_003", "orderStatus": "已完成", "computeType": "匿踪查询"}, {"dataName": "电商用户行为数据计算订单", "contractNumber": "ORDER_004", "contractStartAt": "2023-01-04 09:00:00", "contractEndAt": "2023-01-04 18:00:00", "taskCount": 4, "contractId": "CONTRACT_004", "orderStatus": "待执行", "computeType": "多方安全计算"}], "total": 4}, "taskData": {"records": [{"taskId": "TASK_001", "taskNumber": "T001", "taskName": "智慧城市交通数据隐私计算任务", "taskCompletionTime": "2023-01-01 17:30:00", "transactionHash": "0x1234567890abcdef", "taskStatus": 1002, "contractNumber": "C0001111", "orderNumber": "ORDER_001", "computeType": "隐私求交", "executionStatus": "已完成"}, {"taskId": "TASK_002", "taskNumber": "T002", "taskName": "金融风控数据联邦学习任务", "taskCompletionTime": "2023-01-02 16:45:00", "transactionHash": "0xabcdef1234567890", "taskStatus": 1001, "contractNumber": "C0001112", "orderNumber": "ORDER_002", "computeType": "联邦学习", "executionStatus": "进行中"}, {"taskId": "TASK_003", "taskNumber": "T003", "taskName": "医疗健康数据匿踪查询任务", "taskCompletionTime": "2023-01-03 15:20:00", "transactionHash": "0xdef1234567890abc", "taskStatus": 1002, "contractNumber": "C0001113", "orderNumber": "ORDER_003", "computeType": "匿踪查询", "executionStatus": "已完成"}, {"taskId": "TASK_004", "taskNumber": "T004", "taskName": "电商用户行为多方安全计算任务", "taskCompletionTime": "2023-01-04 14:10:00", "transactionHash": "0x567890abcdef1234", "taskStatus": 1000, "contractNumber": "C0001114", "orderNumber": "ORDER_004", "computeType": "多方安全计算", "executionStatus": "待执行"}], "total": 4}, "taskContentData": {"taskId": "TASK_001", "taskName": "数据隐私计算任务", "taskStatus": "1002", "contractNumber": "C0001111", "taskNumber": "T001", "buyerEnterpriseName": "北京科技有限公司", "sellerEnterpriseName": "杭州数据科技有限公司", "blockNum": "12345678", "contractStartAt": "2023-01-01 09:00:00", "contractEndAt": "2023-01-01 18:00:00", "buyerSocialUnifiedCreditCode": "91110000MA001234XX", "sellerSocialUnifiedCreditCode": "91330100MA28A1234X"}, "billingData": {"records": [{"contractNumber": "C0001111", "orderNumber": "ORDER_001", "dataName": "智慧城市交通数据", "anonymousQueryFee": 100, "privacyIntersectionFee": 200, "privacyCalculationCount": 150, "nodeCost": 450, "billingDate": "2023-01-01", "paymentStatus": "已支付"}, {"contractNumber": "C0001112", "orderNumber": "ORDER_002", "dataName": "金融风控数据集", "anonymousQueryFee": 80, "privacyIntersectionFee": 180, "privacyCalculationCount": 120, "nodeCost": 380, "billingDate": "2023-01-02", "paymentStatus": "已支付"}, {"contractNumber": "C0001113", "orderNumber": "ORDER_003", "dataName": "医疗健康数据", "anonymousQueryFee": 120, "privacyIntersectionFee": 250, "privacyCalculationCount": 180, "nodeCost": 550, "billingDate": "2023-01-03", "paymentStatus": "已支付"}, {"contractNumber": "C0001114", "orderNumber": "ORDER_004", "dataName": "电商用户行为数据", "anonymousQueryFee": 90, "privacyIntersectionFee": 160, "privacyCalculationCount": 100, "nodeCost": 350, "billingDate": "2023-01-04", "paymentStatus": "待支付"}], "total": 4}, "revenueData": {"buyerEnterpriseName": "杭州数据科技有限公司", "totalPrice": 8500, "nodeCost": 1500, "totalRevenue": 10000, "providerShare": 6000, "platformShare": 2000, "operatorShare": 2000, "distributionDate": "2023-01-31"}, "listedVoucherData": {"voucherId": "LV_001", "voucherType": "登记挂牌凭证", "dataName": "L0001111", "enterpriseName": "杭州数据科技有限公司", "socialUnifiedCreditCode": "91330100MA28A1234X", "listedTime": "2023-01-15", "issueDate": "2023-01-15", "validityPeriod": "长期有效", "certificateUrl": "/images/listed_certificate.png"}, "registerVoucherData": {"voucherId": "RV_001", "voucherType": "注册凭证", "dataName": "智慧城市交通数据", "enterpriseName": "杭州数据科技有限公司", "socialUnifiedCreditCode": "91330100MA28A1234X", "createTime": "2023-01-10", "dataRegisterNumber": "A0001111", "issueDate": "2023-01-10", "validityPeriod": "长期有效", "certificateUrl": "/images/register_certificate.png"}, "deliveryRecordData": {"recordId": "DR_001", "deliveryDate": "2023-01-01", "deliveryStatus": "已完成", "deliveryHash": "0xdelivery123456", "verificationResult": "验证通过", "orderCount": 15, "taskCount": 8, "stealthQueryCount": 120, "privacyCalculationCount": 85, "executionDays": 7}, "cascaderOptions": [{"value": "1", "label": "公共数据", "children": [{"value": "11", "label": "城市管理"}, {"value": "12", "label": "交通运输"}]}, {"value": "2", "label": "企业数据", "children": [{"value": "21", "label": "金融"}, {"value": "22", "label": "制造业"}]}], "riskMonitoringData": {"totalEvents": 43, "newEvents": 7, "riskTypeData": [70, 60, 50, 30, 10], "riskTypeLabels": ["敏感数据下载", "异常数据访问", "账号公用", "账号爆破", "接口过频调用"], "riskTrend": {"xData": ["01-01", "01-02", "01-03", "01-04", "01-05", "01-06", "01-07"], "yData": [12, 15, 8, 20, 18, 25, 22]}, "riskDetails": [{"eventId": "RISK_001", "eventType": "敏感数据下载", "eventTime": "2023-01-01 14:30:00", "riskLevel": "高", "description": "检测到异常的敏感数据批量下载行为", "status": "已处理"}, {"eventId": "RISK_002", "eventType": "异常数据访问", "eventTime": "2023-01-01 16:45:00", "riskLevel": "中", "description": "发现非授权时间段的数据访问", "status": "处理中"}, {"eventId": "RISK_003", "eventType": "账号爆破", "eventTime": "2023-01-01 18:20:00", "riskLevel": "高", "description": "检测到多次登录失败尝试", "status": "已处理"}]}, "dataSandboxData": {"settledVendors": 13, "dataResources": 2962, "dataProducts": 321, "applicationScenarios": [{"value": 11.7392043070835, "name": "节能减排"}, {"value": 9.23723855786, "name": "绿色低碳"}, {"value": 7.75434839431, "name": "医疗健康"}, {"value": 11.3865516372, "name": "数据治理"}, {"value": 7.75434839431, "name": "智慧医疗"}, {"value": 5.83541244308, "name": "智能制造"}, {"value": 15.83541244308, "name": "金融服务"}, {"value": 2.83541244308, "name": "智能排产"}, {"value": 5.83541244308, "name": "商贸流通"}, {"value": 10.83541244308, "name": "应急管理"}], "vendorList": [{"vendorId": "V001", "vendorName": "阿里云数据服务", "settledDate": "2023-01-15", "dataCount": 450, "productCount": 68, "status": "正常运营"}, {"vendorId": "V002", "vendorName": "腾讯云数据平台", "settledDate": "2023-02-20", "dataCount": 380, "productCount": 52, "status": "正常运营"}, {"vendorId": "V003", "vendorName": "华为云数据服务", "settledDate": "2023-03-10", "dataCount": 320, "productCount": 45, "status": "正常运营"}, {"vendorId": "V004", "vendorName": "百度智能云", "settledDate": "2023-04-05", "dataCount": 280, "productCount": 38, "status": "正常运营"}, {"vendorId": "V005", "vendorName": "京东云数据", "settledDate": "2023-05-12", "dataCount": 250, "productCount": 32, "status": "正常运营"}], "resourceTrend": {"xData": ["01月", "02月", "03月", "04月", "05月", "06月"], "yData": [1800, 2100, 2350, 2600, 2800, 2962]}, "productTrend": {"xData": ["01月", "02月", "03月", "04月", "05月", "06月"], "yData": [180, 210, 245, 280, 305, 321]}}}}