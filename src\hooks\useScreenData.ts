import { useState, useCallback, useEffect } from 'react'
import { message } from 'antd'
import { useDataCache } from './useDataCache'
import {
  getCount,
  dataTrendChart,
  dataListedCount,
  dataContractListedSignCount,
  dataExList,
  dataCalculatedCount
} from '@/api/screen'

interface ScreenDataState {
  counts: string
  xData: any[]
  yData: any[]
  listedCount: number
  listedCompareRate: number
  signCount: number
  signCompareRate: number
  tradedCount: number
  pendingCount: number
  exchangeData: any[]
  isDataLoaded: boolean
}

/**
 * 大屏数据管理Hook，优化数据加载性能
 */
export function useScreenData() {
  const { getData, clearCache, loading } = useDataCache<any>({
    cacheTime: 5 * 60 * 1000, // 5分钟缓存
    staleTime: 30 * 1000 // 30秒内数据视为新鲜
  })

  const [screenData, setScreenData] = useState<ScreenDataState>({
    counts: '0',
    xData: [],
    yData: [],
    listedCount: 0,
    listedCompareRate: 0,
    signCount: 0,
    signCompareRate: 0,
    tradedCount: 0,
    pendingCount: 0,
    exchangeData: [],
    isDataLoaded: false
  })

  // 获取数据统计
  const fetchDataCount = useCallback(async () => {
    try {
      const result = await getData('dataCount', getCount)
      setScreenData(prev => ({ ...prev, counts: result || '0' }))
      return result
    } catch (error) {
      console.error('获取数据统计失败:', error)
      message.error('获取数据统计失败，请稍后重试')
      return '0'
    }
  }, [getData])

  // 获取趋势图数据
  const fetchTrendChart = useCallback(async () => {
    try {
      const result = await getData('trendChart', dataTrendChart)
      if (result) {
        let xDataResult = []
        let yDataResult = []

        if (result.data) {
          xDataResult = result.data.keyList || result.data.xData || result.data.dates || []
          yDataResult = result.data.valueList || result.data.yData || result.data.values || []
        } else {
          xDataResult = result.keyList || result.xData || result.dates || []
          yDataResult = result.valueList || result.yData || result.values || []
        }

        setScreenData(prev => ({
          ...prev,
          xData: xDataResult,
          yData: yDataResult
        }))
      }
      return result
    } catch (error) {
      console.error('趋势图数据获取失败:', error)
      message.error('获取趋势图数据失败，请稍后重试')
    }
  }, [getData])

  // 获取挂牌数量
  const fetchListedCount = useCallback(async () => {
    try {
      const result = await getData('listedCount', dataListedCount)
      if (result) {
        setScreenData(prev => {
          const totalCount = parseInt(prev.counts) || 1
          const percentage = totalCount > 0 ? Math.round((result / totalCount) * 100) : 0
          return {
            ...prev,
            listedCount: result || 0,
            listedCompareRate: percentage
          }
        })
      }
      return result
    } catch (error) {
      console.error('数据挂牌数量获取失败:', error)
    }
  }, [getData])

  // 获取签约数量
  const fetchSignCount = useCallback(async () => {
    try {
      const result = await getData('signCount', dataContractListedSignCount)
      if (result) {
        setScreenData(prev => {
          const totalCount = parseInt(prev.counts) || 1
          const percentage = totalCount > 0 ? Math.round((result / totalCount) * 100) : 0
          return {
            ...prev,
            signCount: result || 0,
            signCompareRate: percentage
          }
        })
      }
      return result
    } catch (error) {
      console.error('获取数据签约数量失败:', error)
    }
  }, [getData])

  // 获取交易所列表
  const fetchExchangeList = useCallback(async () => {
    try {
      const result = await getData('exchangeList', dataExList)
      if (result) {
        setScreenData(prev => ({ ...prev, exchangeData: result }))
      }
      return result
    } catch (error) {
      console.error('获取交易所列表失败:', error)
    }
  }, [getData])

  // 获取交易统计
  const fetchCalculatedCount = useCallback(async () => {
    try {
      const result = await getData('calculatedCount', dataCalculatedCount)
      if (result) {
        setScreenData(prev => ({
          ...prev,
          tradedCount: result.signedCalculatedCount || 0,
          pendingCount: result.signedButNotCalculatedCount || 0
        }))
      }
      return result
    } catch (error) {
      console.error('获取数据交易统计失败:', error)
      message.error('获取数据交易统计失败，请稍后重试')
    }
  }, [getData])

  // 加载所有初始数据
  const loadInitialData = useCallback(async () => {
    try {
      // 并行请求所有数据，提高加载速度
      const results = await Promise.allSettled([
        fetchDataCount(),
        fetchTrendChart(),
        fetchListedCount(),
        fetchSignCount(),
        fetchExchangeList(),
        fetchCalculatedCount()
      ])

      // 检查是否有失败的请求
      const failedRequests = results.filter(result => result.status === 'rejected')
      if (failedRequests.length > 0) {
        console.warn(`${failedRequests.length} 个请求失败`)
      }

      setScreenData(prev => ({ ...prev, isDataLoaded: true }))
    } catch (error) {
      console.error('初始数据加载失败:', error)
      message.error('数据加载失败，请刷新页面重试')
    }
  }, [fetchDataCount, fetchTrendChart, fetchListedCount, fetchSignCount, fetchExchangeList, fetchCalculatedCount])

  // 刷新所有数据
  const refreshAllData = useCallback(() => {
    clearCache() // 清除缓存
    loadInitialData()
  }, [clearCache, loadInitialData])

  // 刷新特定数据
  const refreshData = useCallback(
    (dataType: string) => {
      clearCache(dataType)
      switch (dataType) {
        case 'dataCount':
          fetchDataCount()
          break
        case 'trendChart':
          fetchTrendChart()
          break
        case 'listedCount':
          fetchListedCount()
          break
        case 'signCount':
          fetchSignCount()
          break
        case 'exchangeList':
          fetchExchangeList()
          break
        case 'calculatedCount':
          fetchCalculatedCount()
          break
        default:
          console.warn('未知的数据类型:', dataType)
      }
    },
    [
      clearCache,
      fetchDataCount,
      fetchTrendChart,
      fetchListedCount,
      fetchSignCount,
      fetchExchangeList,
      fetchCalculatedCount
    ]
  )

  return {
    screenData,
    loading,
    loadInitialData,
    refreshAllData,
    refreshData,
    // 单独的获取函数，供组件按需调用
    fetchDataCount,
    fetchTrendChart,
    fetchListedCount,
    fetchSignCount,
    fetchExchangeList,
    fetchCalculatedCount
  }
}
